(* Question 3: Recursive string removal function *)

(* Recursive function to remove a string from a list *)
fun removeString (inputString, []) = NONE
  | removeString (inputString, head::tail) =
    if head = inputString then
        SOME tail
    else
        case removeString (inputString, tail) of
            NONE => NONE
          | SOME result => SOME (head::result);

(* Helper function to print string list *)
fun printStringList [] = print "[]"
  | printStringList [x] = print ("[\"" ^ x ^ "\"]")
  | printStringList (x::xn) = 
    (print ("[\"" ^ x ^ "\""); 
     app (fn n => print (", \"" ^ n ^ "\"")) xn; 
     print "]");

(* TEST RUNS -  Case Scenario from the question *)
val test1 = removeString ("Xing", ["Hello", "Xing", "World"]);
val test2 = removeString ("Rafi", ["Hello", "Xing", "World"]);