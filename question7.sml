(* Question 7: Full name datatype and equivalent names function *)

(* Tail recursive function to remove a string from a list *)
fun removeStringTail (inputString, inputList) =
    let
        fun helper ([], acc, found) = 
            if found then SOME (rev acc) else NONE
          | helper (head::tail, acc, found) =
            if head = inputString then
                helper (tail, acc, true)
            else
                helper (tail, head::acc, found)
    in
        helper (inputList, [], false)
    end;

(* Define datatype for full name *)
datatype full_name = FullName of {first: string, middle: string, last: string};

(* Function to find name substitutes using tail recursive version *)
fun findSubstitutesTail ([], name) = []
  | findSubstitutesTail (nameList::rest, name) =
    if List.exists (fn x => x = name) nameList then
        case removeStringTail (name, nameList) of
            SOME result => result
          | NONE => []
    else
        findSubstitutesTail (rest, name);

(* Function to find equivalent full names *)
fun findEquivalentNames (nameEquivalents, FullName {first, middle, last}) =
    let
        val substitutes = findSubstitutesTail (nameEquivalents, first)
        fun createFullName firstName = FullName {first=firstName, middle=middle, last=last}
        val originalName = FullName {first=first, middle=middle, last=last}
    in
        originalName :: (map createFullName substitutes)
    end;

(* TEST RUNS -  Case Scenario from the question *)
val nameEquivalents = [["Sitha", "Seetha", "Sita"], 
                      ["Lakshman", "Laxman", "Lucky"], 
                      ["Jacob", "Jake", "Jack"]];

val testName = FullName {first="Laxman", last="Dev", middle="Kumar"};
val equivalentNames = findEquivalentNames (nameEquivalents, testName);
