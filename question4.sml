(* Question 4: Name substitutions function *)

(* Recursive function to remove a string from a list *)
fun removeString (inputString, []) = NONE
  | removeString (inputString, head::tail) =
    if head = inputString then
        SOME tail
    else
        case removeString (inputString, tail) of
            NONE => NONE
          | SOME result => SOME (head::result);

(* Function to print string list *)
fun printStringList [] = print "[]"
  | printStringList [x] = print ("[\"" ^ x ^ "\"]")
  | printStringList (x::xn) = 
    (print ("[\"" ^ x ^ "\""); 
     app (fn n => print (", \"" ^ n ^ "\"")) xn; 
     print "]");

(* Function to find name substitutes *)
fun findSubstitutes ([], name) = []
  | findSubstitutes (nameList::tail, name) =
    if List.exists (fn x => x = name) nameList then
        case removeString (name, nameList) of
            SOME result => result
          | NONE => []
    else
        findSubstitutes (tail, name);

(* TEST RUNS -  <PERSON> Scenario from the question *)
val nameEquivalents = [["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], 
                      ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], 
                      ["<PERSON>", "Jake", "Jack"]];

val substitutes = findSubstitutes (nameEquivalents, "Jacob");