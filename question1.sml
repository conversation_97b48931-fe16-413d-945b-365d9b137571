(* Question 1: Student grade datatype and pass/fail function *)

(* Define datatype for student grade *)
datatype student_grade = StudentGrade of {id: int, grade: int option};

(* Define custom datatype for pass/fail *)
datatype result = Pass | Fail;

(* Function to check if student passes or fails *)
fun checkGrade (StudentGrade {id, grade}) =
    case grade of
        SOME grade => if grade >= 75 then Pass else Fail
      | NONE => Fail;

(* TEST RUNS -  Case Scenario from the question *)
val student = StudentGrade {id=1, grade=SOME 88};

val result = checkGrade student;