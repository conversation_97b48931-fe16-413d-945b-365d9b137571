(* Question 6: Tail recursive version for problem 2 *)

(* Define datatype for student grade *)
datatype student_grade = StudentGrade of {id: int, grade: int option};

(* Define custom datatype for pass/fail *)
datatype result = Pass | Fail;

(* Function to check if student passes or fails *)
fun checkGrade (StudentGrade {id, grade}) =
    case grade of
        SOME g => if g >= 75 then Pass else Fail
      | NONE => Fail;

(* Tail recursive version of countMisgrades *)
fun countMisgradesTail gradeList =
    let
        fun helper ([], acc) = acc
          | helper ((expectedResult, student)::rest, acc) =
            let
                val actualResult = checkGrade student
                val isMisgrade = case (expectedResult, actualResult) of
                    (Pass, Fail) => true
                  | (Fail, Pass) => true
                  | _ => false
                val newAcc = acc + (if isMisgrade then 1 else 0)
            in
                helper (rest, newAcc)
            end
    in
        helper (gradeList, 0)
    end;

(* TEST RUNS -  Case Scenario from the question *)
val grades = [(Fail, StudentGrade {id=1, grade=SOME 88}),
              (Pass, StudentGrade {id=2, grade=SOME 68}),
              (Pass, StudentGrade {id=3, grade=SOME 95})];

val misgrades = countMisgradesTail grades;
