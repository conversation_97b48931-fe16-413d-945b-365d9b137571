(* Question 5: Tail recursive version for problem 1 *)

(* Define datatype for student grade *)
datatype student_grade = StudentGrade of {id: int, grade: int option};

(* Define custom datatype for pass/fail *)
datatype result = Pass | Fail;

(* Tail recursive version of checkGrade *)
fun checkGradeTail (StudentGrade {id, grade}) =
    let
        fun helper (SOME g, acc) = 
            if g >= 75 then Pass else Fail
          | helper (NONE, acc) = Fail
    in
        helper (grade, Pass)
    end;

(* TEST RUNS -  Case Scenario from the question *)
val student = StudentGrade {id=1, grade=SOME 88};
val result = checkGradeTail student;
